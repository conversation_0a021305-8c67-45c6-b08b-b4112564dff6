namespace ArincViewer.Models;

public class ArincDataPoint
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Elevation { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

public class ArincWaypoint : ArincDataPoint
{
    public string? Region { get; set; }
    public string? IcaoCode { get; set; }
    public string? Usage { get; set; }
}

public class ArincNavaid : ArincDataPoint
{
    public string? Frequency { get; set; }
    public string? NavaidClass { get; set; }
    public double? Range { get; set; }
    public string? IcaoCode { get; set; }
}

public class ArincAirport : ArincDataPoint
{
    public string? IcaoCode { get; set; }
    public string? IataCode { get; set; }
    public double? MagneticVariation { get; set; }
    public List<ArincRunway> Runways { get; set; } = new();
}

public class ArincRunway
{
    public string Identifier { get; set; } = string.Empty;
    public double? Length { get; set; }
    public double? Width { get; set; }
    public double? Bearing { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
}

public class ArincAirway : ArincDataPoint
{
    public string? Designation { get; set; }
    public List<ArincDataPoint> Waypoints { get; set; } = new();
    public string? RouteType { get; set; }
    public int? MinimumAltitude { get; set; }
    public int? MaximumAltitude { get; set; }
    public string? Direction { get; set; }
}

public class ArincTerminalWaypoint : ArincDataPoint
{
    public string? Region { get; set; }
    public string? IcaoCode { get; set; }
    public string? Usage { get; set; }
    public string? AirportIdentifier { get; set; }
    public string? ProcedureType { get; set; }
}

public class ArincHoldingPattern : ArincDataPoint
{
    public string? WaypointIdentifier { get; set; }
    public double? InboundCourse { get; set; }
    public string? TurnDirection { get; set; }
    public double? LegLength { get; set; }
    public string? LegType { get; set; }
    public int? MinimumAltitude { get; set; }
    public int? MaximumAltitude { get; set; }
}

public class ArincInstrumentLanding : ArincDataPoint
{
    public string? RunwayIdentifier { get; set; }
    public string? LocalizerFrequency { get; set; }
    public string? GlideSlopeFrequency { get; set; }
    public double? LocalizerBearing { get; set; }
    public double? GlideSlopeAngle { get; set; }
    public string? Category { get; set; }
}

public class ArincCommunication : ArincDataPoint
{
    public string? CommunicationType { get; set; }
    public string? Frequency { get; set; }
    public string? ServiceType { get; set; }
    public string? CallSign { get; set; }
    public string? OperatingHours { get; set; }
}

public class ArincAirspace : ArincDataPoint
{
    public string? AirspaceType { get; set; }
    public string? Classification { get; set; }
    public int? LowerLimit { get; set; }
    public int? UpperLimit { get; set; }
    public string? ControllingAgency { get; set; }
    public List<ArincBoundaryPoint> BoundaryPoints { get; set; } = new();
}

public class ArincBoundaryPoint
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public string? PointType { get; set; }
}

public class ArincSpecialArea : ArincDataPoint
{
    public string? ActivityType { get; set; }
    public string? OperatingTimes { get; set; }
    public int? LowerAltitude { get; set; }
    public int? UpperAltitude { get; set; }
    public string? RestrictiveType { get; set; }
}

public class ArincHeliport : ArincDataPoint
{
    public string? IcaoCode { get; set; }
    public string? IataCode { get; set; }
    public double? MagneticVariation { get; set; }
    public List<ArincHelipad> Helipads { get; set; } = new();
}

public class ArincHelipad : ArincDataPoint
{
    public string? HeliportIdentifier { get; set; }
    public double? Length { get; set; }
    public double? Width { get; set; }
    public double? Bearing { get; set; }
}

public class ArincGate : ArincDataPoint
{
    public string? AirportIdentifier { get; set; }
    public string? TerminalIdentifier { get; set; }
    public string? GateType { get; set; }
}

public class ArincProcedure : ArincDataPoint
{
    public string? ProcedureType { get; set; } // SID, STAR, APPROACH
    public string? ProcedureIdentifier { get; set; }
    public string? RunwayIdentifier { get; set; }
    public string? TransitionIdentifier { get; set; }
    public List<ArincProcedureLeg> Legs { get; set; } = new();
}

public class ArincProcedureLeg
{
    public string? WaypointIdentifier { get; set; }
    public string? PathType { get; set; }
    public double? Course { get; set; }
    public double? Distance { get; set; }
    public int? Altitude { get; set; }
    public string? AltitudeDescription { get; set; }
}

public class ArincTactical : ArincDataPoint
{
    public string? Channel { get; set; }
    public string? TacanClass { get; set; }
    public double? Range { get; set; }
}

public class ArincMarker : ArincDataPoint
{
    public string? MarkerType { get; set; }
    public string? AssociatedRunway { get; set; }
    public string? MarkerClass { get; set; }
}

public class ArincMicrowaveLanding : ArincDataPoint
{
    public string? RunwayIdentifier { get; set; }
    public string? Channel { get; set; }
    public double? Azimuth { get; set; }
    public double? ElevationAngle { get; set; }
    public string? Category { get; set; }
}

public class ArincGlobalLanding : ArincDataPoint
{
    public string? RunwayIdentifier { get; set; }
    public string? ReferencePathIdentifier { get; set; }
    public string? Channel { get; set; }
    public string? ApproachType { get; set; }
}

public class ArincFlightRegion : ArincDataPoint
{
    public string? RegionType { get; set; } // FIR or UIR
    public string? RegionName { get; set; }
    public string? ControllingUnit { get; set; }
    public List<ArincBoundaryPoint> BoundaryPoints { get; set; } = new();
}

public class ArincControlledSpace : ArincDataPoint
{
    public string? AirspaceClass { get; set; }
    public string? ControllingUnit { get; set; }
    public int? LowerLimit { get; set; }
    public int? UpperLimit { get; set; }
    public string? OperatingHours { get; set; }
}

public class ArincRestrictiveSpace : ArincDataPoint
{
    public string? RestrictionType { get; set; }
    public string? OperatingTimes { get; set; }
    public int? LowerAltitude { get; set; }
    public int? UpperAltitude { get; set; }
    public string? Purpose { get; set; }
}

public class ArincAirwayMarker : ArincDataPoint
{
    public string? AirwayIdentifier { get; set; }
    public string? MarkerType { get; set; }
    public double? Bearing { get; set; }
    public double? Distance { get; set; }
}

public class ArincTerminalBeacon : ArincDataPoint
{
    public string? AirportIdentifier { get; set; }
    public string? BeaconClass { get; set; }
    public string? Frequency { get; set; }
    public double? Range { get; set; }
}
