namespace ArincViewer.Models;

public class ArincDataPoint
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Elevation { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

public class ArincWaypoint : ArincDataPoint
{
    public string? Region { get; set; }
    public string? IcaoCode { get; set; }
    public string? Usage { get; set; }
}

public class ArincNavaid : ArincDataPoint
{
    public string? Frequency { get; set; }
    public string? NavaidClass { get; set; }
    public double? Range { get; set; }
    public string? IcaoCode { get; set; }
}

public class ArincAirport : ArincDataPoint
{
    public string? IcaoCode { get; set; }
    public string? IataCode { get; set; }
    public double? MagneticVariation { get; set; }
    public List<ArincRunway> Runways { get; set; } = new();
}

public class ArincRunway
{
    public string Identifier { get; set; } = string.Empty;
    public double? Length { get; set; }
    public double? Width { get; set; }
    public double? Bearing { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
}

public class ArincAirway : ArincDataPoint
{
    public string? Designation { get; set; }
    public List<ArincDataPoint> Waypoints { get; set; } = new();
    public string? RouteType { get; set; }
}
