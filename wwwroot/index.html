<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARINC 424 Data Viewer</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>ARINC 424 Data Viewer</h1>
            <p>Visualize aviation navigation data on an interactive map</p>
        </header>

        <div class="controls">
            <div class="control-group">
                <button id="loadSampleBtn" class="btn btn-primary">Load Sample Data</button>
                <button id="clearMapBtn" class="btn btn-secondary">Clear Map</button>
            </div>
            
            <div class="control-group">
                <input type="file" id="fileInput" accept=".txt,.dat,.424" style="display: none;">
                <button id="uploadBtn" class="btn btn-primary">Upload ARINC File</button>
            </div>

            <div class="control-group">
                <label>
                    <input type="checkbox" id="showWaypoints" checked> Waypoints
                </label>
                <label>
                    <input type="checkbox" id="showNavaids" checked> Navaids
                </label>
                <label>
                    <input type="checkbox" id="showAirports" checked> Airports
                </label>
            </div>
        </div>

        <div class="map-container">
            <div id="map"></div>
        </div>

        <div class="info-panel" id="infoPanel">
            <h3>Data Point Information</h3>
            <div id="infoContent">
                <p>Click on a marker to see details</p>
            </div>
        </div>

        <div class="status" id="status">
            Ready
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Custom JS -->
    <script src="js/map.js"></script>
</body>
</html>
