// Global variables
let map;
let markersLayer;
let currentData = [];

// Initialize the map
function initMap() {
    // Create map centered on the US
    map = L.map('map').setView([39.8283, -98.5795], 4);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Create a layer group for markers
    markersLayer = L.layerGroup().addTo(map);

    updateStatus('Map initialized');
}

// Create custom markers based on data type
function createMarker(dataPoint) {
    const lat = dataPoint.latitude;
    const lng = dataPoint.longitude;
    
    if (!lat || !lng) {
        console.warn('Invalid coordinates for data point:', dataPoint);
        return null;
    }

    let iconHtml = '';
    let className = '';
    
    switch (dataPoint.type.toLowerCase()) {
        case 'waypoint':
            iconHtml = '●';
            className = 'waypoint-marker';
            break;
        case 'navaid':
        case 'vor':
        case 'ndb':
            iconHtml = '◆';
            className = 'navaid-marker';
            break;
        case 'airport':
            iconHtml = '✈';
            className = 'airport-marker';
            break;
        default:
            iconHtml = '○';
            className = 'waypoint-marker';
    }

    const customIcon = L.divIcon({
        html: iconHtml,
        className: className,
        iconSize: [16, 16],
        iconAnchor: [8, 8]
    });

    const marker = L.marker([lat, lng], { icon: customIcon });
    
    // Create popup content
    const popupContent = createPopupContent(dataPoint);
    marker.bindPopup(popupContent);
    
    // Add click event to update info panel
    marker.on('click', () => {
        updateInfoPanel(dataPoint);
    });

    return marker;
}

// Create popup content for markers
function createPopupContent(dataPoint) {
    let content = `
        <div class="popup-content">
            <h4>${dataPoint.name}</h4>
            <p><strong>Type:</strong> ${dataPoint.type}</p>
            <p><strong>ID:</strong> ${dataPoint.id}</p>
            <p><strong>Coordinates:</strong> ${dataPoint.latitude.toFixed(6)}, ${dataPoint.longitude.toFixed(6)}</p>
    `;
    
    if (dataPoint.icaoCode) {
        content += `<p><strong>ICAO:</strong> ${dataPoint.icaoCode}</p>`;
    }
    
    if (dataPoint.frequency) {
        content += `<p><strong>Frequency:</strong> ${dataPoint.frequency}</p>`;
    }
    
    if (dataPoint.elevation) {
        content += `<p><strong>Elevation:</strong> ${dataPoint.elevation} ft</p>`;
    }
    
    content += '</div>';
    return content;
}

// Update info panel with data point details
function updateInfoPanel(dataPoint) {
    const infoContent = document.getElementById('infoContent');
    
    let content = `
        <h4>${dataPoint.name}</h4>
        <p><strong>Type:</strong> ${dataPoint.type}</p>
        <p><strong>ID:</strong> ${dataPoint.id}</p>
        <p><strong>Coordinates:</strong> ${dataPoint.latitude.toFixed(6)}, ${dataPoint.longitude.toFixed(6)}</p>
    `;
    
    if (dataPoint.icaoCode) {
        content += `<p><strong>ICAO Code:</strong> ${dataPoint.icaoCode}</p>`;
    }
    
    if (dataPoint.frequency) {
        content += `<p><strong>Frequency:</strong> ${dataPoint.frequency}</p>`;
    }
    
    if (dataPoint.elevation) {
        content += `<p><strong>Elevation:</strong> ${dataPoint.elevation} ft</p>`;
    }
    
    if (dataPoint.region) {
        content += `<p><strong>Region:</strong> ${dataPoint.region}</p>`;
    }
    
    if (dataPoint.usage) {
        content += `<p><strong>Usage:</strong> ${dataPoint.usage}</p>`;
    }
    
    infoContent.innerHTML = content;
}

// Load and display data on the map
function displayData(data) {
    currentData = data;
    updateMapDisplay();
    
    if (data.length > 0) {
        // Fit map to show all markers
        const group = new L.featureGroup(markersLayer.getLayers());
        if (group.getBounds().isValid()) {
            map.fitBounds(group.getBounds().pad(0.1));
        }
        
        updateStatus(`Loaded ${data.length} data points`);
    } else {
        updateStatus('No data points to display');
    }
}

// Update map display based on filter checkboxes
function updateMapDisplay() {
    const showWaypoints = document.getElementById('showWaypoints').checked;
    const showNavaids = document.getElementById('showNavaids').checked;
    const showAirports = document.getElementById('showAirports').checked;
    
    // Clear existing markers
    markersLayer.clearLayers();
    
    // Add filtered markers
    currentData.forEach(dataPoint => {
        const type = dataPoint.type.toLowerCase();
        let shouldShow = false;
        
        if (type === 'waypoint' && showWaypoints) shouldShow = true;
        if ((type === 'navaid' || type === 'vor' || type === 'ndb') && showNavaids) shouldShow = true;
        if (type === 'airport' && showAirports) shouldShow = true;
        
        if (shouldShow) {
            const marker = createMarker(dataPoint);
            if (marker) {
                markersLayer.addLayer(marker);
            }
        }
    });
}

// Update status message
function updateStatus(message) {
    document.getElementById('status').textContent = message;
}

// Load sample data
async function loadSampleData() {
    try {
        updateStatus('Loading sample data...');
        
        const response = await fetch('/api/arincdata/sample');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        displayData(data);
    } catch (error) {
        console.error('Error loading sample data:', error);
        updateStatus('Error loading sample data');
        alert('Error loading sample data. Please check the console for details.');
    }
}

// Clear map
function clearMap() {
    markersLayer.clearLayers();
    currentData = [];
    document.getElementById('infoContent').innerHTML = '<p>Click on a marker to see details</p>';
    updateStatus('Map cleared');
}

// Handle file upload
async function handleFileUpload(file) {
    try {
        updateStatus('Uploading and parsing file...');
        
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch('/api/arincdata/upload', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        displayData(data);
    } catch (error) {
        console.error('Error uploading file:', error);
        updateStatus('Error uploading file');
        alert('Error uploading file. Please check the console for details.');
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initMap();
    
    // Event listeners
    document.getElementById('loadSampleBtn').addEventListener('click', loadSampleData);
    document.getElementById('clearMapBtn').addEventListener('click', clearMap);
    
    document.getElementById('uploadBtn').addEventListener('click', function() {
        document.getElementById('fileInput').click();
    });
    
    document.getElementById('fileInput').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleFileUpload(file);
        }
    });
    
    // Filter checkboxes
    document.getElementById('showWaypoints').addEventListener('change', updateMapDisplay);
    document.getElementById('showNavaids').addEventListener('change', updateMapDisplay);
    document.getElementById('showAirports').addEventListener('change', updateMapDisplay);
});
