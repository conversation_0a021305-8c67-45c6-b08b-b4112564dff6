* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

header {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 1rem 2rem;
    text-align: center;
}

header h1 {
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

header p {
    opacity: 0.9;
    font-size: 1.1rem;
}

.controls {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #ddd;
    display: flex;
    gap: 2rem;
    align-items: flex-start;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-section {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    width: 100%;
}

.filter-section h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.filter-category {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.filter-category h4 {
    margin: 0 0 0.75rem 0;
    color: #495057;
    font-size: 1rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.filter-category label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
}

.filter-category label:hover {
    color: #3498db;
}

.advanced-filters {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-bottom: 1rem;
}

.advanced-filters h4 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1rem;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
}

.filter-row label {
    min-width: 120px;
    font-weight: 500;
}

.filter-row input {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.control-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.control-group input[type="checkbox"] {
    margin: 0;
}

.map-container {
    flex: 1;
    position: relative;
}

#map {
    height: 100%;
    width: 100%;
}

.info-panel {
    background: white;
    border-top: 1px solid #ddd;
    padding: 1rem 2rem;
    max-height: 200px;
    overflow-y: auto;
}

.info-panel h3 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

#infoContent {
    font-size: 0.9rem;
    line-height: 1.4;
}

.status {
    background: #34495e;
    color: white;
    padding: 0.5rem 2rem;
    font-size: 0.8rem;
    text-align: center;
}

/* Custom marker styles */
.waypoint-marker {
    background-color: #e74c3c;
    border: 2px solid white;
    border-radius: 50%;
    width: 12px;
    height: 12px;
}

.navaid-marker {
    background-color: #f39c12;
    border: 2px solid white;
    border-radius: 50%;
    width: 14px;
    height: 14px;
}

.airport-marker {
    background-color: #27ae60;
    border: 2px solid white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
    .controls {
        flex-direction: column;
        gap: 1rem;
    }
    
    .control-group {
        justify-content: center;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    header p {
        font-size: 1rem;
    }
}
