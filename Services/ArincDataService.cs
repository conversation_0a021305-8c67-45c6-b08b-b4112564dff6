using Arinc424;
using Arinc424.Building;
using ArincViewer.Models;
using System.Reflection;

namespace ArincViewer.Services;

public interface IArincDataService
{
    Task<List<ArincDataPoint>> ParseArincDataAsync(string[] arincLines);
    Task<List<ArincDataPoint>> GetSampleDataAsync();
}

public class ArincDataService : IArincDataService
{
    private readonly ILogger<ArincDataService> _logger;

    public ArincDataService(ILogger<ArincDataService> logger)
    {
        _logger = logger;
    }

    public async Task<List<ArincDataPoint>> ParseArincDataAsync(string[] arincLines)
    {
        try
        {
            _logger.LogInformation("Starting ARINC data parsing for {LineCount} lines", arincLines.Length);

            // Create metadata for ARINC 424 parsing
            var meta = Meta424.Create(Supplement.V20);
            
            // Parse the ARINC data
            var data = Data424.Create(meta, arincLines, out var invalid, out var skipped);
            
            _logger.LogInformation("Parsed ARINC data: {InvalidCount} invalid, {SkippedCount} skipped", 
                invalid.Count, skipped.Count);

            var dataPoints = new List<ArincDataPoint>();

            // Extract enroute waypoints
            if (data.EnrouteWaypoints != null)
            {
                foreach (var waypoint in data.EnrouteWaypoints)
                {
                    var point = new ArincWaypoint
                    {
                        Id = waypoint.Identifier ?? Guid.NewGuid().ToString(),
                        Name = waypoint.Name ?? waypoint.Identifier ?? "Unknown",
                        Type = "Waypoint",
                        Latitude = waypoint.Coordinates.Latitude,
                        Longitude = waypoint.Coordinates.Longitude,
                        IcaoCode = waypoint.Icao.ToString(),
                        Usage = waypoint.Usages.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract VHF navaids (VOR/DME)
            if (data.Omnidirectionals != null)
            {
                foreach (var navaid in data.Omnidirectionals)
                {
                    var point = new ArincNavaid
                    {
                        Id = navaid.Identifier ?? Guid.NewGuid().ToString(),
                        Name = navaid.Name ?? navaid.Identifier ?? "Unknown",
                        Type = "VOR",
                        Latitude = navaid.Coordinates.Latitude,
                        Longitude = navaid.Coordinates.Longitude,
                        Frequency = navaid.Frequency.ToString(),
                        IcaoCode = navaid.Icao.ToString(),
                        NavaidClass = navaid.Type.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract NDB navaids
            if (data.Nondirectionals != null)
            {
                foreach (var navaid in data.Nondirectionals)
                {
                    var point = new ArincNavaid
                    {
                        Id = navaid.Identifier ?? Guid.NewGuid().ToString(),
                        Name = navaid.Name ?? navaid.Identifier ?? "Unknown",
                        Type = "NDB",
                        Latitude = navaid.Coordinates.Latitude,
                        Longitude = navaid.Coordinates.Longitude,
                        Frequency = navaid.Frequency.ToString(),
                        IcaoCode = navaid.Icao.ToString(),
                        NavaidClass = "NDB"
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract airports
            if (data.Airports != null)
            {
                foreach (var airport in data.Airports)
                {
                    var point = new ArincAirport
                    {
                        Id = airport.Identifier ?? Guid.NewGuid().ToString(),
                        Name = airport.Name ?? airport.Identifier ?? "Unknown",
                        Type = "Airport",
                        Latitude = airport.Coordinates.Latitude,
                        Longitude = airport.Coordinates.Longitude,
                        IcaoCode = airport.Icao.ToString(),
                        MagneticVariation = airport.Variation
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract heliports
            if (data.Heliports != null)
            {
                foreach (var heliport in data.Heliports)
                {
                    var point = new ArincHeliport
                    {
                        Id = heliport.Identifier ?? Guid.NewGuid().ToString(),
                        Name = heliport.Name ?? heliport.Identifier ?? "Unknown",
                        Type = "Heliport",
                        Latitude = heliport.Coordinates.Latitude,
                        Longitude = heliport.Coordinates.Longitude,
                        IcaoCode = heliport.Icao.ToString(),
                        MagneticVariation = heliport.Variation
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract terminal waypoints (airport)
            if (data.AirportTerminalWaypoints != null)
            {
                foreach (var waypoint in data.AirportTerminalWaypoints)
                {
                    var point = new ArincTerminalWaypoint
                    {
                        Id = waypoint.Identifier ?? Guid.NewGuid().ToString(),
                        Name = waypoint.Name ?? waypoint.Identifier ?? "Unknown",
                        Type = "Terminal Waypoint",
                        Latitude = waypoint.Coordinates.Latitude,
                        Longitude = waypoint.Coordinates.Longitude,
                        IcaoCode = waypoint.Icao.ToString(),
                        Usage = waypoint.Usages.ToString(),
                        AirportIdentifier = waypoint.Airport?.Identifier
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract terminal waypoints (heliport)
            if (data.HeliportTerminalWaypoints != null)
            {
                foreach (var waypoint in data.HeliportTerminalWaypoints)
                {
                    var point = new ArincTerminalWaypoint
                    {
                        Id = waypoint.Identifier ?? Guid.NewGuid().ToString(),
                        Name = waypoint.Name ?? waypoint.Identifier ?? "Unknown",
                        Type = "Heliport Terminal Waypoint",
                        Latitude = waypoint.Coordinates.Latitude,
                        Longitude = waypoint.Coordinates.Longitude,
                        IcaoCode = waypoint.Icao.ToString(),
                        Usage = waypoint.Usages.ToString(),
                        AirportIdentifier = waypoint.Heliport?.Identifier
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract holding patterns
            if (data.HoldingPatterns != null)
            {
                foreach (var holding in data.HoldingPatterns)
                {
                    var point = new ArincHoldingPattern
                    {
                        Id = holding.Identifier ?? Guid.NewGuid().ToString(),
                        Name = $"Holding Pattern {holding.Identifier}",
                        Type = "Holding Pattern",
                        Latitude = holding.Coordinates.Latitude,
                        Longitude = holding.Coordinates.Longitude,
                        WaypointIdentifier = holding.Waypoint?.Identifier,
                        InboundCourse = holding.InboundCourse,
                        TurnDirection = holding.TurnDirection.ToString(),
                        LegLength = holding.LegLength,
                        LegType = holding.LegType.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract TACAN navaids
            if (data.Tacticals != null)
            {
                foreach (var tacan in data.Tacticals)
                {
                    var point = new ArincTactical
                    {
                        Id = tacan.Identifier ?? Guid.NewGuid().ToString(),
                        Name = tacan.Name ?? tacan.Identifier ?? "Unknown",
                        Type = "TACAN",
                        Latitude = tacan.Coordinates.Latitude,
                        Longitude = tacan.Coordinates.Longitude,
                        Channel = tacan.Channel.ToString(),
                        TacanClass = tacan.NavaidClass.ToString(),
                        IcaoCode = tacan.Icao.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract airways
            if (data.Airways != null)
            {
                foreach (var airway in data.Airways)
                {
                    var point = new ArincAirway
                    {
                        Id = airway.Identifier ?? Guid.NewGuid().ToString(),
                        Name = $"Airway {airway.Identifier}",
                        Type = "Airway",
                        Latitude = airway.Coordinates.Latitude,
                        Longitude = airway.Coordinates.Longitude,
                        Designation = airway.Identifier,
                        RouteType = airway.RouteType.ToString(),
                        Direction = airway.Direction.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract airway markers
            if (data.AirwayMarkers != null)
            {
                foreach (var marker in data.AirwayMarkers)
                {
                    var point = new ArincAirwayMarker
                    {
                        Id = marker.Identifier ?? Guid.NewGuid().ToString(),
                        Name = $"Airway Marker {marker.Identifier}",
                        Type = "Airway Marker",
                        Latitude = marker.Coordinates.Latitude,
                        Longitude = marker.Coordinates.Longitude,
                        AirwayIdentifier = marker.Airway?.Identifier
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract instrument landing systems
            if (data.InstrumentLandings != null)
            {
                foreach (var ils in data.InstrumentLandings)
                {
                    var point = new ArincInstrumentLanding
                    {
                        Id = ils.Identifier ?? Guid.NewGuid().ToString(),
                        Name = $"ILS {ils.Identifier}",
                        Type = "ILS",
                        Latitude = ils.Coordinates.Latitude,
                        Longitude = ils.Coordinates.Longitude,
                        LocalizerFrequency = ils.Frequency.ToString(),
                        LocalizerBearing = ils.LocalizerBearing,
                        Category = ils.Category.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract microwave landing systems
            if (data.MicrowaveLandings != null)
            {
                foreach (var mls in data.MicrowaveLandings)
                {
                    var point = new ArincMicrowaveLanding
                    {
                        Id = mls.Identifier ?? Guid.NewGuid().ToString(),
                        Name = $"MLS {mls.Identifier}",
                        Type = "MLS",
                        Latitude = mls.Coordinates.Latitude,
                        Longitude = mls.Coordinates.Longitude,
                        Channel = mls.Channel.ToString(),
                        Category = mls.Category.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract global landing systems
            if (data.GlobalLandings != null)
            {
                foreach (var gls in data.GlobalLandings)
                {
                    var point = new ArincGlobalLanding
                    {
                        Id = gls.Identifier ?? Guid.NewGuid().ToString(),
                        Name = $"GLS {gls.Identifier}",
                        Type = "GLS",
                        Latitude = gls.Coordinates.Latitude,
                        Longitude = gls.Coordinates.Longitude,
                        Channel = gls.Channel.ToString(),
                        ApproachType = gls.ApproachType.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract terminal beacons
            if (data.TerminalBeacons != null)
            {
                foreach (var beacon in data.TerminalBeacons)
                {
                    var point = new ArincTerminalBeacon
                    {
                        Id = beacon.Identifier ?? Guid.NewGuid().ToString(),
                        Name = beacon.Name ?? beacon.Identifier ?? "Unknown",
                        Type = "Terminal NDB",
                        Latitude = beacon.Coordinates.Latitude,
                        Longitude = beacon.Coordinates.Longitude,
                        Frequency = beacon.Frequency.ToString(),
                        BeaconClass = beacon.NavaidClass.ToString(),
                        IcaoCode = beacon.Icao.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract airport communications
            if (data.AirportCommunications != null)
            {
                foreach (var comm in data.AirportCommunications)
                {
                    var point = new ArincCommunication
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = $"{comm.CommunicationType} {comm.CallSign}",
                        Type = "Airport Communication",
                        Latitude = comm.Coordinates.Latitude,
                        Longitude = comm.Coordinates.Longitude,
                        CommunicationType = comm.CommunicationType.ToString(),
                        Frequency = comm.Frequency.ToString(),
                        CallSign = comm.CallSign,
                        ServiceType = comm.ServiceType.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract heliport communications
            if (data.HeliportCommunications != null)
            {
                foreach (var comm in data.HeliportCommunications)
                {
                    var point = new ArincCommunication
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = $"{comm.CommunicationType} {comm.CallSign}",
                        Type = "Heliport Communication",
                        Latitude = comm.Coordinates.Latitude,
                        Longitude = comm.Coordinates.Longitude,
                        CommunicationType = comm.CommunicationType.ToString(),
                        Frequency = comm.Frequency.ToString(),
                        CallSign = comm.CallSign,
                        ServiceType = comm.ServiceType.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract controlled airspace
            if (data.ControlledSpaces != null)
            {
                foreach (var airspace in data.ControlledSpaces)
                {
                    var point = new ArincControlledSpace
                    {
                        Id = airspace.Identifier ?? Guid.NewGuid().ToString(),
                        Name = airspace.Name ?? airspace.Identifier ?? "Unknown",
                        Type = "Controlled Airspace",
                        Latitude = airspace.Coordinates.Latitude,
                        Longitude = airspace.Coordinates.Longitude,
                        AirspaceClass = airspace.AirspaceClass.ToString(),
                        ControllingUnit = airspace.ControllingUnit
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract restrictive airspace
            if (data.RestrictiveSpaces != null)
            {
                foreach (var airspace in data.RestrictiveSpaces)
                {
                    var point = new ArincRestrictiveSpace
                    {
                        Id = airspace.Identifier ?? Guid.NewGuid().ToString(),
                        Name = airspace.Name ?? airspace.Identifier ?? "Unknown",
                        Type = "Restrictive Airspace",
                        Latitude = airspace.Coordinates.Latitude,
                        Longitude = airspace.Coordinates.Longitude,
                        RestrictionType = airspace.RestrictionType.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract special activity areas
            if (data.SpecialAreas != null)
            {
                foreach (var area in data.SpecialAreas)
                {
                    var point = new ArincSpecialArea
                    {
                        Id = area.Identifier ?? Guid.NewGuid().ToString(),
                        Name = area.Name ?? area.Identifier ?? "Unknown",
                        Type = "Special Activity Area",
                        Latitude = area.Coordinates.Latitude,
                        Longitude = area.Coordinates.Longitude,
                        ActivityType = area.ActivityType.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract flight regions (FIR/UIR)
            if (data.FlightRegions != null)
            {
                foreach (var region in data.FlightRegions)
                {
                    var point = new ArincFlightRegion
                    {
                        Id = region.Identifier ?? Guid.NewGuid().ToString(),
                        Name = region.Name ?? region.Identifier ?? "Unknown",
                        Type = "Flight Region",
                        Latitude = region.Coordinates.Latitude,
                        Longitude = region.Coordinates.Longitude,
                        RegionType = region.RegionType.ToString(),
                        RegionName = region.Name
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract gates
            if (data.Gates != null)
            {
                foreach (var gate in data.Gates)
                {
                    var point = new ArincGate
                    {
                        Id = gate.Identifier ?? Guid.NewGuid().ToString(),
                        Name = $"Gate {gate.Identifier}",
                        Type = "Gate",
                        Latitude = gate.Coordinates.Latitude,
                        Longitude = gate.Coordinates.Longitude,
                        AirportIdentifier = gate.Airport?.Identifier
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract runway thresholds
            if (data.Thresholds != null)
            {
                foreach (var threshold in data.Thresholds)
                {
                    var point = new ArincRunway
                    {
                        Identifier = threshold.Identifier ?? "Unknown",
                        Latitude = threshold.Coordinates.Latitude,
                        Longitude = threshold.Coordinates.Longitude,
                        Length = threshold.Length,
                        Width = threshold.Width,
                        Bearing = threshold.TrueBearing
                    };

                    var dataPoint = new ArincDataPoint
                    {
                        Id = threshold.Identifier ?? Guid.NewGuid().ToString(),
                        Name = $"Runway {threshold.Identifier}",
                        Type = "Runway",
                        Latitude = threshold.Coordinates.Latitude,
                        Longitude = threshold.Coordinates.Longitude,
                        Elevation = threshold.Elevation
                    };

                    dataPoints.Add(dataPoint);
                }
            }

            _logger.LogInformation("Extracted {DataPointCount} data points", dataPoints.Count);
            return dataPoints;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing ARINC data");
            throw;
        }
    }

    public async Task<List<ArincDataPoint>> GetSampleDataAsync()
    {
        // Return comprehensive sample data for testing all data types
        var sampleData = new List<ArincDataPoint>
        {
            // Airports
            new ArincAirport
            {
                Id = "KORD",
                Name = "Chicago O'Hare International Airport",
                Type = "Airport",
                Latitude = 41.9786,
                Longitude = -87.9048,
                IcaoCode = "KORD",
                MagneticVariation = -3.5
            },
            new ArincAirport
            {
                Id = "KLAX",
                Name = "Los Angeles International Airport",
                Type = "Airport",
                Latitude = 34.0522,
                Longitude = -118.2437,
                IcaoCode = "KLAX",
                MagneticVariation = 12.8
            },
            new ArincAirport
            {
                Id = "KBOS",
                Name = "Boston Logan International Airport",
                Type = "Airport",
                Latitude = 42.3656,
                Longitude = -71.0096,
                IcaoCode = "KBOS",
                MagneticVariation = -14.2
            },

            // VOR Navaids
            new ArincNavaid
            {
                Id = "ORD",
                Name = "Chicago O'Hare VOR",
                Type = "VOR",
                Latitude = 41.9786,
                Longitude = -87.9048,
                Frequency = "113.90",
                NavaidClass = "VOR",
                IcaoCode = "KORD"
            },
            new ArincNavaid
            {
                Id = "LAX",
                Name = "Los Angeles VOR",
                Type = "VOR",
                Latitude = 34.0522,
                Longitude = -118.2437,
                Frequency = "113.60",
                NavaidClass = "VOR",
                IcaoCode = "KLAX"
            },

            // NDB Navaids
            new ArincNavaid
            {
                Id = "CH",
                Name = "Chicago Heights NDB",
                Type = "NDB",
                Latitude = 41.5061,
                Longitude = -87.6358,
                Frequency = "521",
                NavaidClass = "NDB"
            },

            // Waypoints
            new ArincWaypoint
            {
                Id = "WYNDE",
                Name = "WYNDE Intersection",
                Type = "Waypoint",
                Latitude = 41.5,
                Longitude = -87.5,
                Usage = "RNAV"
            },
            new ArincWaypoint
            {
                Id = "PETTY",
                Name = "PETTY Intersection",
                Type = "Waypoint",
                Latitude = 41.8,
                Longitude = -87.8,
                Usage = "RNAV"
            },

            // Terminal Waypoints
            new ArincTerminalWaypoint
            {
                Id = "BRICKYARD",
                Name = "BRICKYARD",
                Type = "Terminal Waypoint",
                Latitude = 39.7173,
                Longitude = -86.2478,
                AirportIdentifier = "KIND",
                Usage = "Terminal"
            },

            // Holding Patterns
            new ArincHoldingPattern
            {
                Id = "HOLD_WYNDE",
                Name = "WYNDE Holding Pattern",
                Type = "Holding Pattern",
                Latitude = 41.5,
                Longitude = -87.5,
                WaypointIdentifier = "WYNDE",
                InboundCourse = 270,
                TurnDirection = "Right",
                LegLength = 4.0,
                MinimumAltitude = 3000,
                MaximumAltitude = 10000
            },

            // ILS Systems
            new ArincInstrumentLanding
            {
                Id = "IORD10L",
                Name = "ORD ILS RWY 10L",
                Type = "ILS",
                Latitude = 41.9786,
                Longitude = -87.9048,
                RunwayIdentifier = "10L",
                LocalizerFrequency = "111.30",
                LocalizerBearing = 104.0,
                Category = "CAT_I"
            },

            // TACAN
            new ArincTactical
            {
                Id = "ORD",
                Name = "Chicago O'Hare TACAN",
                Type = "TACAN",
                Latitude = 41.9786,
                Longitude = -87.9048,
                Channel = "108X",
                TacanClass = "Terminal"
            },

            // Airways
            new ArincAirway
            {
                Id = "V6",
                Name = "Victor 6",
                Type = "Airway",
                Latitude = 41.7,
                Longitude = -87.7,
                Designation = "V6",
                RouteType = "Victor",
                MinimumAltitude = 2500,
                MaximumAltitude = 18000,
                Direction = "Bidirectional"
            },

            // Communications
            new ArincCommunication
            {
                Id = "ORD_TWR",
                Name = "O'Hare Tower",
                Type = "Airport Communication",
                Latitude = 41.9786,
                Longitude = -87.9048,
                CommunicationType = "Tower",
                Frequency = "120.15",
                CallSign = "O'Hare Tower",
                ServiceType = "ATC"
            },
            new ArincCommunication
            {
                Id = "ORD_GND",
                Name = "O'Hare Ground",
                Type = "Airport Communication",
                Latitude = 41.9786,
                Longitude = -87.9048,
                CommunicationType = "Ground",
                Frequency = "121.67",
                CallSign = "O'Hare Ground",
                ServiceType = "ATC"
            },

            // Airspace
            new ArincControlledSpace
            {
                Id = "ORD_CLASS_B",
                Name = "Chicago Class B Airspace",
                Type = "Controlled Airspace",
                Latitude = 41.9786,
                Longitude = -87.9048,
                AirspaceClass = "B",
                ControllingUnit = "Chicago TRACON",
                LowerLimit = 0,
                UpperLimit = 10000
            },

            // Special Areas
            new ArincSpecialArea
            {
                Id = "R2508",
                Name = "Restricted Area R-2508",
                Type = "Special Activity Area",
                Latitude = 42.1,
                Longitude = -87.9,
                ActivityType = "Military",
                RestrictiveType = "Restricted",
                LowerAltitude = 0,
                UpperAltitude = 8000
            },

            // Heliports
            new ArincHeliport
            {
                Id = "IL01",
                Name = "Northwestern Memorial Hospital Heliport",
                Type = "Heliport",
                Latitude = 41.8955,
                Longitude = -87.6217,
                IcaoCode = "IL01"
            },

            // Gates
            new ArincGate
            {
                Id = "B12",
                Name = "Gate B12",
                Type = "Gate",
                Latitude = 41.9786,
                Longitude = -87.9048,
                AirportIdentifier = "KORD",
                TerminalIdentifier = "B",
                GateType = "Passenger"
            }
        };

        return await Task.FromResult(sampleData);
    }
}
