using Arinc424;
using Arinc424.Building;
using ArincViewer.Models;
using System.Reflection;

namespace ArincViewer.Services;

public interface IArincDataService
{
    Task<List<ArincDataPoint>> ParseArincDataAsync(string[] arincLines);
    Task<List<ArincDataPoint>> GetSampleDataAsync();
}

public class ArincDataService : IArincDataService
{
    private readonly ILogger<ArincDataService> _logger;

    public ArincDataService(ILogger<ArincDataService> logger)
    {
        _logger = logger;
    }

    public async Task<List<ArincDataPoint>> ParseArincDataAsync(string[] arincLines)
    {
        try
        {
            _logger.LogInformation("Starting ARINC data parsing for {LineCount} lines", arincLines.Length);

            // Create metadata for ARINC 424 parsing
            var meta = Meta424.Create(Supplement.V20);
            
            // Parse the ARINC data
            var data = Data424.Create(meta, arincLines, out var invalid, out var skipped);
            
            _logger.LogInformation("Parsed ARINC data: {InvalidCount} invalid, {SkippedCount} skipped", 
                invalid.Count, skipped.Count);

            var dataPoints = new List<ArincDataPoint>();

            // Extract enroute waypoints
            if (data.EnrouteWaypoints != null)
            {
                foreach (var waypoint in data.EnrouteWaypoints)
                {
                    var point = new ArincWaypoint
                    {
                        Id = waypoint.Identifier ?? Guid.NewGuid().ToString(),
                        Name = waypoint.Name ?? waypoint.Identifier ?? "Unknown",
                        Type = "Waypoint",
                        Latitude = waypoint.Coordinate?.Latitude ?? 0,
                        Longitude = waypoint.Coordinate?.Longitude ?? 0,
                        Region = waypoint.Region?.ToString(),
                        IcaoCode = waypoint.IcaoCode,
                        Usage = waypoint.Usage?.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract VHF navaids (VOR/DME)
            if (data.Omnidirectionals != null)
            {
                foreach (var navaid in data.Omnidirectionals)
                {
                    var point = new ArincNavaid
                    {
                        Id = navaid.Identifier ?? Guid.NewGuid().ToString(),
                        Name = navaid.Name ?? navaid.Identifier ?? "Unknown",
                        Type = "VOR",
                        Latitude = navaid.Coordinate?.Latitude ?? 0,
                        Longitude = navaid.Coordinate?.Longitude ?? 0,
                        Frequency = navaid.Frequency?.ToString(),
                        IcaoCode = navaid.IcaoCode,
                        NavaidClass = navaid.NavaidClass?.ToString()
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract NDB navaids
            if (data.Nondirectionals != null)
            {
                foreach (var navaid in data.Nondirectionals)
                {
                    var point = new ArincNavaid
                    {
                        Id = navaid.Identifier ?? Guid.NewGuid().ToString(),
                        Name = navaid.Name ?? navaid.Identifier ?? "Unknown",
                        Type = "NDB",
                        Latitude = navaid.Coordinate?.Latitude ?? 0,
                        Longitude = navaid.Coordinate?.Longitude ?? 0,
                        Frequency = navaid.Frequency?.ToString(),
                        IcaoCode = navaid.IcaoCode,
                        NavaidClass = "NDB"
                    };

                    dataPoints.Add(point);
                }
            }

            // Extract airports
            if (data.Airports != null)
            {
                foreach (var airport in data.Airports)
                {
                    var point = new ArincAirport
                    {
                        Id = airport.Identifier ?? Guid.NewGuid().ToString(),
                        Name = airport.Name ?? airport.Identifier ?? "Unknown",
                        Type = "Airport",
                        Latitude = airport.Coordinate?.Latitude ?? 0,
                        Longitude = airport.Coordinate?.Longitude ?? 0,
                        IcaoCode = airport.IcaoCode,
                        MagneticVariation = airport.MagneticVariation
                    };

                    dataPoints.Add(point);
                }
            }

            _logger.LogInformation("Extracted {DataPointCount} data points", dataPoints.Count);
            return dataPoints;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing ARINC data");
            throw;
        }
    }

    public async Task<List<ArincDataPoint>> GetSampleDataAsync()
    {
        // Return some sample data for testing
        var sampleData = new List<ArincDataPoint>
        {
            new ArincWaypoint
            {
                Id = "KORD",
                Name = "Chicago O'Hare International Airport",
                Type = "Airport",
                Latitude = 41.9786,
                Longitude = -87.9048,
                IcaoCode = "KORD"
            },
            new ArincWaypoint
            {
                Id = "KLAX",
                Name = "Los Angeles International Airport",
                Type = "Airport",
                Latitude = 34.0522,
                Longitude = -118.2437,
                IcaoCode = "KLAX"
            },
            new ArincNavaid
            {
                Id = "ORD",
                Name = "Chicago O'Hare VOR",
                Type = "VOR",
                Latitude = 41.9786,
                Longitude = -87.9048,
                Frequency = "113.90",
                NavaidClass = "VOR"
            },
            new ArincWaypoint
            {
                Id = "WYNDE",
                Name = "WYNDE Intersection",
                Type = "Waypoint",
                Latitude = 41.5,
                Longitude = -87.5
            },
            new ArincWaypoint
            {
                Id = "KBOS",
                Name = "Boston Logan International Airport",
                Type = "Airport",
                Latitude = 42.3656,
                Longitude = -71.0096,
                IcaoCode = "KBOS"
            }
        };

        return await Task.FromResult(sampleData);
    }
}
