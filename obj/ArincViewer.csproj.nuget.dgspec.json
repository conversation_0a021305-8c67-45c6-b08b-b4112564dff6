{"format": 1, "restore": {"/home/<USER>/Documents/augment-projects/ArincViewer/ArincViewer.csproj": {}}, "projects": {"/home/<USER>/Documents/augment-projects/ArincViewer/ArincViewer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/augment-projects/ArincViewer/ArincViewer.csproj", "projectName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectPath": "/home/<USER>/Documents/augment-projects/ArincViewer/ArincViewer.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/augment-projects/ArincViewer/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.3.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "arinc424": {"target": "Package", "version": "[0.1.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}}}