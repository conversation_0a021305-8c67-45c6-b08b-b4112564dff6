using Microsoft.AspNetCore.Mvc;
using ArincViewer.Models;
using ArincViewer.Services;

namespace ArincViewer.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ArincDataController : ControllerBase
{
    private readonly IArincDataService _arincDataService;
    private readonly ILogger<ArincDataController> _logger;

    public ArincDataController(IArincDataService arincDataService, ILogger<ArincDataController> logger)
    {
        _arincDataService = arincDataService;
        _logger = logger;
    }

    [HttpGet("sample")]
    public async Task<ActionResult<List<ArincDataPoint>>> GetSampleData()
    {
        try
        {
            var data = await _arincDataService.GetSampleDataAsync();
            return Ok(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sample data");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("parse")]
    public async Task<ActionResult<List<ArincDataPoint>>> ParseArincData([FromBody] ArincDataRequest request)
    {
        try
        {
            if (request?.Data == null || !request.Data.Any())
            {
                return BadRequest("No ARINC data provided");
            }

            var data = await _arincDataService.ParseArincDataAsync(request.Data);
            return Ok(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing ARINC data");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("upload")]
    public async Task<ActionResult<List<ArincDataPoint>>> UploadArincFile(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded");
            }

            using var reader = new StreamReader(file.OpenReadStream());
            var content = await reader.ReadToEndAsync();
            var lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            var data = await _arincDataService.ParseArincDataAsync(lines);
            return Ok(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing uploaded file");
            return StatusCode(500, "Internal server error");
        }
    }
}

public class ArincDataRequest
{
    public string[] Data { get; set; } = Array.Empty<string>();
}
