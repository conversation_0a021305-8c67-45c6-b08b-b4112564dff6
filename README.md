# ARINC 424 Data Viewer

A web application for visualizing ARINC 424 aviation navigation data on interactive maps using the [arinc424.net](https://github.com/malstraem/arinc424.net) library.

## Features

- **Interactive Map Visualization**: Display ARINC 424 data points on a Leaflet-based interactive map
- **Multiple Data Types**: Support for waypoints, navaids (VOR/NDB), and airports
- **File Upload**: Upload and parse ARINC 424 data files
- **Sample Data**: Built-in sample data for testing and demonstration
- **Filtering**: Toggle visibility of different data types (waypoints, navaids, airports)
- **Detailed Information**: Click on markers to view detailed information about each data point

## Technology Stack

- **Backend**: ASP.NET Core 8.0 Web API
- **Frontend**: HTML5, CSS3, JavaScript with Leaflet.js for mapping
- **ARINC Parser**: [arinc424.net](https://github.com/malstraem/arinc424.net) library
- **Map Tiles**: OpenStreetMap

## Getting Started

### Prerequisites

- .NET 8.0 SDK
- Modern web browser

### Installation

1. Clone or download this repository
2. Navigate to the project directory
3. Restore dependencies:
   ```bash
   dotnet restore
   ```

### Running the Application

1. Build and run the application:
   ```bash
   dotnet run
   ```

2. Open your web browser and navigate to:
   ```
   http://localhost:5132
   ```

## Usage

### Loading Sample Data

1. Click the "Load Sample Data" button to display sample aviation data points
2. The map will automatically zoom to show all data points
3. Use the checkboxes to filter different types of data

### Uploading ARINC Files

1. Click the "Upload ARINC File" button
2. Select an ARINC 424 format file (.txt, .dat, .424)
3. The application will parse the file and display the data points on the map

### Interacting with the Map

- **Zoom**: Use mouse wheel or zoom controls
- **Pan**: Click and drag to move around the map
- **Marker Details**: Click on any marker to see detailed information
- **Filtering**: Use the checkboxes to show/hide different data types

## API Endpoints

The application provides REST API endpoints for programmatic access:

### GET /api/arincdata/sample
Returns sample ARINC data points for testing.

### POST /api/arincdata/upload
Upload and parse an ARINC 424 file.
- Content-Type: multipart/form-data
- Body: file upload

### POST /api/arincdata/parse
Parse ARINC 424 data from raw text lines.
- Content-Type: application/json
- Body: `{"data": ["line1", "line2", ...]}`

## Data Types Supported

- **Waypoints**: Navigation waypoints and intersections
- **VOR Navaids**: VHF Omnidirectional Range stations
- **NDB Navaids**: Non-Directional Beacon stations
- **Airports**: Airport reference points

## File Formats

The application supports ARINC 424 format files with the following extensions:
- `.txt` - Text files
- `.dat` - Data files
- `.424` - ARINC 424 files

## Development

### Project Structure

```
ArincViewer/
├── Controllers/          # API controllers
├── Services/            # Business logic services
├── Models/              # Data models
├── wwwroot/            # Static web files
│   ├── css/            # Stylesheets
│   ├── js/             # JavaScript files
│   └── index.html      # Main web page
└── Data/               # Sample data files
```

### Key Components

- **ArincDataService**: Handles ARINC 424 data parsing using the arinc424.net library
- **ArincDataController**: REST API endpoints for data access
- **Map Interface**: Interactive Leaflet.js map with custom markers

## Testing

Run the included test script to verify API functionality:

```bash
python3 test_arinc_parsing.py
```

## Troubleshooting

### Common Issues

1. **Build Errors**: Ensure .NET 8.0 SDK is installed
2. **Map Not Loading**: Check browser console for JavaScript errors
3. **File Upload Issues**: Verify file format is valid ARINC 424

### Logs

Check the console output when running the application for detailed error messages and parsing information.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project uses the MIT license. See the arinc424.net library documentation for its licensing terms.

## Acknowledgments

- [arinc424.net](https://github.com/malstraem/arinc424.net) - ARINC 424 parsing library
- [Leaflet.js](https://leafletjs.com/) - Interactive mapping library
- [OpenStreetMap](https://www.openstreetmap.org/) - Map tile provider

## Support

For issues related to ARINC 424 parsing, please refer to the [arinc424.net documentation](https://malstraem.github.io/arinc424.net/).

For application-specific issues, please check the console logs and ensure all dependencies are properly installed.
