#!/usr/bin/env python3
"""
Simple test script to verify the ARINC data parsing API
"""
import requests
import json

def test_sample_data():
    """Test the sample data endpoint"""
    print("Testing sample data endpoint...")
    try:
        response = requests.get("http://localhost:5132/api/arincdata/sample")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Sample data endpoint working - received {len(data)} data points")
            for point in data[:3]:  # Show first 3 points
                print(f"  - {point['name']} ({point['type']}) at {point['latitude']}, {point['longitude']}")
        else:
            print(f"✗ Sample data endpoint failed with status {response.status_code}")
    except Exception as e:
        print(f"✗ Error testing sample data: {e}")

def test_file_upload():
    """Test the file upload endpoint"""
    print("\nTesting file upload endpoint...")
    try:
        with open("Data/sample_arinc.dat", "rb") as f:
            files = {"file": f}
            response = requests.post("http://localhost:5132/api/arincdata/upload", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ File upload endpoint working - parsed {len(data)} data points")
            for point in data[:3]:  # Show first 3 points
                print(f"  - {point['name']} ({point['type']}) at {point['latitude']}, {point['longitude']}")
        else:
            print(f"✗ File upload endpoint failed with status {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"✗ Error testing file upload: {e}")

def test_parse_data():
    """Test the parse data endpoint"""
    print("\nTesting parse data endpoint...")
    try:
        # Sample ARINC data lines
        sample_lines = [
            "SUSAP KORDPA01010A0040N087540W0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000",
            "SUSAP KLAXPA01010A0033N118240W0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
        ]
        
        payload = {"data": sample_lines}
        response = requests.post("http://localhost:5132/api/arincdata/parse", 
                               json=payload,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Parse data endpoint working - parsed {len(data)} data points")
            for point in data:
                print(f"  - {point['name']} ({point['type']}) at {point['latitude']}, {point['longitude']}")
        else:
            print(f"✗ Parse data endpoint failed with status {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"✗ Error testing parse data: {e}")

if __name__ == "__main__":
    print("ARINC Viewer API Test")
    print("=" * 50)
    
    test_sample_data()
    test_file_upload()
    test_parse_data()
    
    print("\n" + "=" * 50)
    print("Test completed!")
